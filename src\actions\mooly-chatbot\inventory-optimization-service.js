'use client';

/**
 * Inventory Optimization Service
 * Dịch vụ tối ưu hóa quản lý tồn kho với:
 * - Single-warehouse logic (logic kho đơn)
 * - Real-time stock updates (cập nhật tồn kho thời gian thực)
 * - Smart alerts (cảnh báo thông minh)
 * - Inventory forecasting (dự báo tồn kho)
 */

import { BUSINESS_TYPES } from './business-config-service';
import { callRPC, fetchData, createData, updateData } from './supabase-utils';

// Inventory related tables
const PRODUCTS_TABLE = 'products';
const PRODUCT_VARIANTS_TABLE = 'product_variants';
const INVENTORY_TRANSACTIONS_TABLE = 'inventory_transactions';
const INVENTORY_ALERTS_TABLE = 'inventory_alerts';
const STOCK_MOVEMENTS_TABLE = 'stock_movements';

// Transaction types
export const INVENTORY_TRANSACTION_TYPES = {
  PURCHASE: 'purchase',        // Nhập hàng
  SALE: 'sale',               // Bán hàng
  ADJUSTMENT: 'adjustment',    // Điều chỉnh
  RETURN: 'return',           // Trả hàng
  DAMAGE: 'damage',           // Hư hỏng
  TRANSFER: 'transfer'        // Chuyển kho
};

// Alert types
export const INVENTORY_ALERT_TYPES = {
  LOW_STOCK: 'low_stock',
  OUT_OF_STOCK: 'out_of_stock',
  OVERSTOCK: 'overstock',
  EXPIRING_SOON: 'expiring_soon'
};

/**
 * 📦 REAL-TIME STOCK MANAGEMENT FUNCTIONS
 */

/**
 * Cập nhật tồn kho thời gian thực
 * @param {string} productId - ID sản phẩm
 * @param {string} variantId - ID biến thể (optional)
 * @param {number} quantityChange - Thay đổi số lượng (có thể âm)
 * @param {string} transactionType - Loại giao dịch
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function updateStockRealtime(productId, variantId, quantityChange, transactionType, options = {}) {
  try {
    const {
      note = '',
      orderId = null,
      userId = null,
      autoCreateAlert = true,
      businessType = null
    } = options;

    // 1. Validate business type supports inventory
    if (businessType) {
      const businessConfig = BUSINESS_TYPES[businessType];
      if (businessConfig && !businessConfig.features.inventory) {
        return {
          success: false,
          error: 'Loại hình kinh doanh này không hỗ trợ quản lý tồn kho'
        };
      }
    }

    // 2. Get current stock
    const currentStock = await getCurrentStock(productId, variantId);
    if (!currentStock.success) {
      return currentStock;
    }

    const currentQuantity = currentStock.data.quantity;
    const newQuantity = Math.max(0, currentQuantity + quantityChange);

    // 3. Update stock in database
    const updateResult = await updateStockInDatabase(productId, variantId, newQuantity);
    if (!updateResult.success) {
      return updateResult;
    }

    // 4. Create inventory transaction record
    await createInventoryTransaction({
      productId,
      variantId,
      transactionType,
      quantityBefore: currentQuantity,
      quantityChange,
      quantityAfter: newQuantity,
      note,
      orderId,
      userId
    });

    // 5. Create stock movement record
    await createStockMovement({
      productId,
      variantId,
      movementType: transactionType,
      quantity: Math.abs(quantityChange),
      direction: quantityChange >= 0 ? 'in' : 'out',
      note,
      orderId
    });

    // 6. Check and create alerts if needed
    if (autoCreateAlert) {
      await checkAndCreateStockAlerts(productId, variantId, newQuantity);
    }

    return {
      success: true,
      data: {
        productId,
        variantId,
        previousQuantity: currentQuantity,
        newQuantity,
        quantityChange
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật tồn kho'
    };
  }
}

/**
 * Lấy tồn kho hiện tại
 * @param {string} productId - ID sản phẩm
 * @param {string} variantId - ID biến thể (optional)
 * @returns {Promise<Object>} - Kết quả truy vấn
 */
async function getCurrentStock(productId, variantId) {
  try {
    if (variantId) {
      // Get variant stock
      const result = await fetchData(PRODUCT_VARIANTS_TABLE, {
        filters: { id: variantId, productId },
        single: true,
        columns: 'id, stockQuantity'
      });

      return {
        success: true,
        data: {
          quantity: result.data?.stockQuantity || 0,
          isVariant: true
        }
      };
    } else {
      // Get product stock
      const result = await fetchData(PRODUCTS_TABLE, {
        filters: { id: productId },
        single: true,
        columns: 'id, stockQuantity, hasVariants'
      });

      if (result.data?.hasVariants) {
        return {
          success: false,
          error: 'Sản phẩm có biến thể, cần chỉ định variantId'
        };
      }

      return {
        success: true,
        data: {
          quantity: result.data?.stockQuantity || 0,
          isVariant: false
        }
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy tồn kho hiện tại'
    };
  }
}

/**
 * Cập nhật tồn kho trong database sử dụng stored procedure
 * @param {string} productId - ID sản phẩm
 * @param {string} variantId - ID biến thể (optional)
 * @param {number} newQuantity - Số lượng mới
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
async function updateStockInDatabase(productId, variantId, newQuantity) {
  try {
    // Lấy số lượng hiện tại để tính toán thay đổi
    let currentQuantity = 0;

    if (variantId) {
      const { success, data } = await fetchData(PRODUCT_VARIANTS_TABLE, {
        filters: { id: variantId },
        columns: 'stock_quantity',
        single: true
      });
      if (success && data) {
        currentQuantity = data.stockQuantity || 0;
      }
    } else {
      const { success, data } = await fetchData(PRODUCTS_TABLE, {
        filters: { id: productId },
        columns: 'stock_quantity',
        single: true
      });
      if (success && data) {
        currentQuantity = data.stockQuantity || 0;
      }
    }

    // Tính toán thay đổi số lượng
    const quantityChange = newQuantity - currentQuantity;

    // Sử dụng stored procedure để cập nhật tồn kho và ghi lịch sử tự động
    return callRPC('update_product_inventory', {
      product_id: productId,
      quantity_change: quantityChange,
      variant_id: variantId,
      reference_id: null,
      reference_type: 'inventory_optimization',
      notes: 'Cập nhật tồn kho từ inventory optimization service'
    });
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật tồn kho trong database'
    };
  }
}

/**
 * Tạo bản ghi giao dịch tồn kho
 * @param {Object} transactionData - Dữ liệu giao dịch
 * @returns {Promise<Object>} - Kết quả tạo
 */
async function createInventoryTransaction(transactionData) {
  return createData(INVENTORY_TRANSACTIONS_TABLE, {
    ...transactionData,
    createdAt: new Date().toISOString()
  });
}

/**
 * Tạo bản ghi di chuyển kho
 * @param {Object} movementData - Dữ liệu di chuyển
 * @returns {Promise<Object>} - Kết quả tạo
 */
async function createStockMovement(movementData) {
  return createData(STOCK_MOVEMENTS_TABLE, {
    ...movementData,
    createdAt: new Date().toISOString()
  });
}

/**
 * 🚨 SMART ALERTS FUNCTIONS
 */

/**
 * Kiểm tra và tạo cảnh báo tồn kho
 * @param {string} productId - ID sản phẩm
 * @param {string} variantId - ID biến thể (optional)
 * @param {number} currentQuantity - Số lượng hiện tại
 * @returns {Promise<Object>} - Kết quả kiểm tra
 */
async function checkAndCreateStockAlerts(productId, variantId, currentQuantity) {
  try {
    // Get product/variant info for alert thresholds
    const productInfo = await getProductAlertThresholds(productId, variantId);
    if (!productInfo.success) {
      return productInfo;
    }

    const { lowStockThreshold, overstockThreshold } = productInfo.data;
    const alerts = [];

    // Check for low stock
    if (currentQuantity <= 0) {
      alerts.push({
        type: INVENTORY_ALERT_TYPES.OUT_OF_STOCK,
        severity: 'critical',
        message: 'Sản phẩm đã hết hàng'
      });
    } else if (currentQuantity <= lowStockThreshold) {
      alerts.push({
        type: INVENTORY_ALERT_TYPES.LOW_STOCK,
        severity: 'warning',
        message: `Sản phẩm sắp hết hàng (còn ${currentQuantity} sản phẩm)`
      });
    }

    // Check for overstock
    if (overstockThreshold && currentQuantity >= overstockThreshold) {
      alerts.push({
        type: INVENTORY_ALERT_TYPES.OVERSTOCK,
        severity: 'info',
        message: `Sản phẩm tồn kho quá nhiều (${currentQuantity} sản phẩm)`
      });
    }

    // Create alerts
    for (const alert of alerts) {
      await createInventoryAlert({
        productId,
        variantId,
        alertType: alert.type,
        severity: alert.severity,
        message: alert.message,
        currentQuantity,
        threshold: alert.type === INVENTORY_ALERT_TYPES.LOW_STOCK ? lowStockThreshold : overstockThreshold
      });
    }

    return {
      success: true,
      data: {
        alertsCreated: alerts.length,
        alerts
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi kiểm tra cảnh báo tồn kho'
    };
  }
}

/**
 * Lấy ngưỡng cảnh báo của sản phẩm
 * @param {string} productId - ID sản phẩm
 * @param {string} variantId - ID biến thể (optional)
 * @returns {Promise<Object>} - Kết quả truy vấn
 */
async function getProductAlertThresholds(productId, variantId) {
  try {
    if (variantId) {
      // Get variant thresholds
      const result = await fetchData(PRODUCT_VARIANTS_TABLE, {
        filters: { id: variantId },
        single: true,
        columns: 'id, lowStockThreshold, overstockThreshold'
      });

      return {
        success: true,
        data: {
          lowStockThreshold: result.data?.lowStockThreshold || 10,
          overstockThreshold: result.data?.overstockThreshold || null
        }
      };
    } else {
      // Get product thresholds
      const result = await fetchData(PRODUCTS_TABLE, {
        filters: { id: productId },
        single: true,
        columns: 'id, lowStockThreshold, overstockThreshold'
      });

      return {
        success: true,
        data: {
          lowStockThreshold: result.data?.lowStockThreshold || 10,
          overstockThreshold: result.data?.overstockThreshold || null
        }
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy ngưỡng cảnh báo'
    };
  }
}

/**
 * Tạo cảnh báo tồn kho
 * @param {Object} alertData - Dữ liệu cảnh báo
 * @returns {Promise<Object>} - Kết quả tạo
 */
async function createInventoryAlert(alertData) {
  // Check if similar alert already exists and is not resolved
  const existingAlert = await fetchData(INVENTORY_ALERTS_TABLE, {
    filters: {
      productId: alertData.productId,
      variantId: alertData.variantId,
      alertType: alertData.alertType,
      isResolved: false
    },
    single: true
  });

  // Don't create duplicate alerts
  if (existingAlert.success && existingAlert.data) {
    return { success: true, data: existingAlert.data };
  }

  return createData(INVENTORY_ALERTS_TABLE, {
    ...alertData,
    isResolved: false,
    createdAt: new Date().toISOString()
  });
}

/**
 * Lấy danh sách cảnh báo tồn kho
 * @param {Object} options - Tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả truy vấn
 */
export async function getInventoryAlerts(options = {}) {
  const {
    isResolved = false,
    alertType = null,
    severity = null,
  } = options;

  let filters = { isResolved };

  if (alertType) {
    filters.alertType = alertType;
  }

  if (severity) {
    filters.severity = severity;
  }

  return fetchData(INVENTORY_ALERTS_TABLE, {
    filters,
    orderBy: 'createdAt',
    ascending: false,
    columns: `
      *,
      product:products(id, name, images),
      variant:product_variants(id, name, avatar)
    `
  });
}

/**
 * Đánh dấu cảnh báo đã được giải quyết
 * @param {string} alertId - ID cảnh báo
 * @param {string} resolvedNote - Ghi chú giải quyết
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function resolveInventoryAlert(alertId, resolvedNote = '') {
  return updateData(INVENTORY_ALERTS_TABLE, alertId, {
    isResolved: true,
    resolvedAt: new Date().toISOString(),
    resolvedNote
  });
}

/**
 * 📊 INVENTORY FORECASTING FUNCTIONS
 */

/**
 * Dự báo tồn kho dựa trên lịch sử bán hàng
 * @param {string} productId - ID sản phẩm
 * @param {string} variantId - ID biến thể (optional)
 * @param {Object} options - Tùy chọn dự báo
 * @returns {Promise<Object>} - Kết quả dự báo
 */
export async function forecastInventory(productId, variantId, options = {}) {
  try {
    const {
      forecastDays = 30,
      historicalDays = 90,
      includeSeasonality = true,
      businessType = null
    } = options;

    // 1. Validate business type supports inventory
    if (businessType) {
      const businessConfig = BUSINESS_TYPES[businessType];
      if (businessConfig && !businessConfig.features.inventory) {
        return {
          success: false,
          error: 'Loại hình kinh doanh này không hỗ trợ quản lý tồn kho'
        };
      }
    }

    // 2. Get historical sales data
    const salesHistory = await getHistoricalSalesData(productId, variantId, historicalDays);
    if (!salesHistory.success) {
      return salesHistory;
    }

    // 3. Get current stock
    const currentStock = await getCurrentStock(productId, variantId);
    if (!currentStock.success) {
      return currentStock;
    }

    // 4. Calculate forecast
    const forecast = calculateInventoryForecast(
      salesHistory.data,
      currentStock.data.quantity,
      forecastDays,
      includeSeasonality
    );

    // 5. Generate recommendations
    const recommendations = generateInventoryRecommendations(forecast, currentStock.data.quantity);

    return {
      success: true,
      data: {
        productId,
        variantId,
        currentStock: currentStock.data.quantity,
        forecastDays,
        forecast,
        recommendations,
        generatedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi dự báo tồn kho'
    };
  }
}

/**
 * Lấy dữ liệu lịch sử bán hàng
 * @param {string} productId - ID sản phẩm
 * @param {string} variantId - ID biến thể (optional)
 * @param {number} days - Số ngày lịch sử
 * @returns {Promise<Object>} - Kết quả truy vấn
 */
async function getHistoricalSalesData(productId, variantId, days) {
  try {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);

    // Use RPC function to get aggregated sales data
    const result = await callRPC('get_historical_sales_data', {
      product_id: productId,
      variant_id: variantId,
      from_date: fromDate.toISOString(),
      to_date: new Date().toISOString()
    });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy dữ liệu lịch sử bán hàng'
    };
  }
}

/**
 * Tính toán dự báo tồn kho
 * @param {Array} salesHistory - Lịch sử bán hàng
 * @param {number} currentStock - Tồn kho hiện tại
 * @param {number} forecastDays - Số ngày dự báo
 * @param {boolean} includeSeasonality - Có tính seasonality không
 * @returns {Object} - Kết quả dự báo
 */
function calculateInventoryForecast(salesHistory, currentStock, forecastDays, includeSeasonality) {
  try {
    if (!salesHistory || salesHistory.length === 0) {
      return {
        averageDailySales: 0,
        projectedSales: 0,
        projectedStock: currentStock,
        stockoutDate: null,
        daysUntilStockout: null
      };
    }

    // Calculate average daily sales
    const totalSales = salesHistory.reduce((sum, day) => sum + (day.quantity || 0), 0);
    const averageDailySales = totalSales / salesHistory.length;

    // Apply seasonality adjustment if enabled
    let adjustedDailySales = averageDailySales;
    if (includeSeasonality) {
      const seasonalityFactor = calculateSeasonalityFactor(salesHistory);
      adjustedDailySales = averageDailySales * seasonalityFactor;
    }

    // Calculate projections
    const projectedSales = adjustedDailySales * forecastDays;
    const projectedStock = Math.max(0, currentStock - projectedSales);

    // Calculate stockout prediction
    let stockoutDate = null;
    let daysUntilStockout = null;

    if (adjustedDailySales > 0) {
      daysUntilStockout = Math.floor(currentStock / adjustedDailySales);
      if (daysUntilStockout <= forecastDays) {
        stockoutDate = new Date();
        stockoutDate.setDate(stockoutDate.getDate() + daysUntilStockout);
      }
    }

    return {
      averageDailySales: Math.round(averageDailySales * 100) / 100,
      adjustedDailySales: Math.round(adjustedDailySales * 100) / 100,
      projectedSales: Math.round(projectedSales),
      projectedStock: Math.round(projectedStock),
      stockoutDate: stockoutDate?.toISOString() || null,
      daysUntilStockout
    };
  } catch (error) {
    console.error('Error calculating inventory forecast:', error);
    return {
      averageDailySales: 0,
      projectedSales: 0,
      projectedStock: currentStock,
      stockoutDate: null,
      daysUntilStockout: null
    };
  }
}

/**
 * Tính toán hệ số seasonality
 * @param {Array} salesHistory - Lịch sử bán hàng
 * @returns {number} - Hệ số seasonality
 */
function calculateSeasonalityFactor(salesHistory) {
  try {
    // Simple seasonality calculation based on recent vs older data
    const recentDays = 7;
    const recentData = salesHistory.slice(-recentDays);
    const olderData = salesHistory.slice(0, -recentDays);

    if (recentData.length === 0 || olderData.length === 0) {
      return 1.0; // No seasonality adjustment
    }

    const recentAverage = recentData.reduce((sum, day) => sum + (day.quantity || 0), 0) / recentData.length;
    const olderAverage = olderData.reduce((sum, day) => sum + (day.quantity || 0), 0) / olderData.length;

    if (olderAverage === 0) {
      return 1.0;
    }

    // Cap the seasonality factor between 0.5 and 2.0
    const factor = recentAverage / olderAverage;
    return Math.max(0.5, Math.min(2.0, factor));
  } catch (error) {
    console.error('Error calculating seasonality factor:', error);
    return 1.0;
  }
}

/**
 * Tạo khuyến nghị tồn kho
 * @param {Object} forecast - Dự báo
 * @param {number} currentStock - Tồn kho hiện tại
 * @returns {Array} - Danh sách khuyến nghị
 */
function generateInventoryRecommendations(forecast, currentStock) {
  const recommendations = [];

  // Stockout warning
  if (forecast.daysUntilStockout && forecast.daysUntilStockout <= 7) {
    recommendations.push({
      type: 'urgent',
      title: 'Cảnh báo hết hàng',
      message: `Sản phẩm sẽ hết hàng trong ${forecast.daysUntilStockout} ngày`,
      action: 'Cần nhập hàng ngay lập tức'
    });
  } else if (forecast.daysUntilStockout && forecast.daysUntilStockout <= 14) {
    recommendations.push({
      type: 'warning',
      title: 'Sắp hết hàng',
      message: `Sản phẩm sẽ hết hàng trong ${forecast.daysUntilStockout} ngày`,
      action: 'Nên chuẩn bị nhập hàng'
    });
  }

  // Reorder recommendation
  if (forecast.adjustedDailySales > 0) {
    const recommendedReorderQuantity = Math.ceil(forecast.adjustedDailySales * 30); // 30 days supply
    recommendations.push({
      type: 'info',
      title: 'Khuyến nghị nhập hàng',
      message: `Nên nhập ${recommendedReorderQuantity} sản phẩm để đủ dùng 30 ngày`,
      action: `Đặt hàng ${recommendedReorderQuantity} sản phẩm`
    });
  }

  // Overstock warning
  if (forecast.adjustedDailySales > 0) {
    const daysOfSupply = currentStock / forecast.adjustedDailySales;
    if (daysOfSupply > 90) {
      recommendations.push({
        type: 'warning',
        title: 'Tồn kho quá nhiều',
        message: `Tồn kho hiện tại đủ dùng ${Math.round(daysOfSupply)} ngày`,
        action: 'Cân nhắc giảm giá hoặc khuyến mãi'
      });
    }
  }

  return recommendations;
}

/**
 * 📦 BULK INVENTORY OPERATIONS
 */

/**
 * Cập nhật tồn kho hàng loạt
 * @param {Array} updates - Danh sách cập nhật
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function bulkUpdateInventory(updates, options = {}) {
  try {
    const {
      transactionType = INVENTORY_TRANSACTION_TYPES.ADJUSTMENT,
      note = 'Cập nhật hàng loạt',
      userId = null
    } = options;

    const results = [];
    const errors = [];

    for (const update of updates) {
      try {
        const { productId, variantId, newQuantity } = update;

        // Get current stock
        const currentStock = await getCurrentStock(productId, variantId);
        if (!currentStock.success) {
          errors.push({
            productId,
            variantId,
            error: currentStock.error
          });
          continue;
        }

        const quantityChange = newQuantity - currentStock.data.quantity;

        // Update stock
        const updateResult = await updateStockRealtime(
          productId,
          variantId,
          quantityChange,
          transactionType,
          { note, userId, autoCreateAlert: true }
        );

        if (updateResult.success) {
          results.push({
            productId,
            variantId,
            previousQuantity: currentStock.data.quantity,
            newQuantity,
            quantityChange
          });
        } else {
          errors.push({
            productId,
            variantId,
            error: updateResult.error
          });
        }
      } catch (error) {
        errors.push({
          productId: update.productId,
          variantId: update.variantId,
          error: error.message
        });
      }
    }

    return {
      success: true,
      data: {
        successCount: results.length,
        errorCount: errors.length,
        results,
        errors
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật tồn kho hàng loạt'
    };
  }
}
