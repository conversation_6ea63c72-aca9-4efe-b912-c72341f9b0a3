'use client';

import PropTypes from 'prop-types';

import Link from '@mui/material/Link';
import Avatar from '@mui/material/Avatar';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import Typography from '@mui/material/Typography';
import ListItemText from '@mui/material/ListItemText';

import { fDateTime } from 'src/utils/format-time';

import { Label } from 'src/components/label';

// ----------------------------------------------------------------------

InventoryHistoryTableRow.propTypes = {
  row: PropTypes.object,
};

export default function InventoryHistoryTableRow({ row }) {
  const {
    type,
    quantity,
    previousQuantity,
    currentQuantity,
    createdAt,
    productName,
    variantName,
    productAvatar,
    productSku,
    variantSku,
    displayType,
    quantityDisplay,
    notes,
  } = row;

  // Xác định màu sắc cho loại giao dịch
  const getTransactionTypeColor = (transactionType) => {
    switch (transactionType) {
      case 'adjustment':
        return 'info';
      case 'purchase':
      case 'stock_in':
      case 'return':
        return 'success';
      case 'sale':
      case 'stock_out':
        return 'error';
      case 'transfer':
        return 'warning';
      case 'initial':
        return 'primary';
      default:
        return 'default';
    }
  };

  // Xác định nhãn cho loại giao dịch
  const getTransactionTypeLabel = (transactionType) => {
    switch (transactionType) {
      case 'adjustment':
        return 'Điều chỉnh';
      case 'purchase':
      case 'stock_in':
        return 'Nhập kho';
      case 'stock_out':
        return 'Xuất kho';
      case 'sale':
        return 'Bán hàng';
      case 'return':
        return 'Trả hàng';
      case 'transfer':
        return 'Chuyển kho';
      case 'initial':
        return 'Khởi tạo';
      default:
        return transactionType;
    }
  };

  // Định dạng ngày giờ
  const formattedDate = createdAt
    ? fDateTime(new Date(createdAt), 'DD/MM/YYYY HH:mm')
    : 'N/A';

  // Lấy hình ảnh sản phẩm
  const productCover = productAvatar;

  return (
    <TableRow
      sx={{
        '&:hover': {
          backgroundColor: (theme) => theme.palette.action.hover,
          '& .MuiTableCell-root': {
            borderBottomColor: (theme) => theme.palette.divider,
          },
        },
        '& .MuiTableCell-root': {
          borderBottom: '1px dashed',
          borderBottomColor: (theme) => theme.palette.divider,
          py: 1.5,
        },
      }}
    >
      <TableCell>
        <Typography variant="body2">{formattedDate}</Typography>
      </TableCell>

      <TableCell>
        <Label variant="soft" color={getTransactionTypeColor(type)}>
          {displayType || getTransactionTypeLabel(type)}
        </Label>
      </TableCell>

      <TableCell sx={{ display: 'flex', alignItems: 'center', minWidth: 200 }}>
        <Avatar
          alt={productName}
          src={productCover}
          variant="rounded"
          sx={{ width: 40, height: 40, mr: 2, borderRadius: 1 }}
        />

        <ListItemText
          disableTypography
          primary={
            <Link
              noWrap
              color="inherit"
              variant="subtitle2"
              sx={{ cursor: 'pointer', display: 'block' }}
            >
              {productName}
            </Link>
          }
          secondary={
            <Typography variant="body2" color="text.disabled" noWrap>
              {productSku}
            </Typography>
          }
        />
      </TableCell>

      <TableCell>
        <Typography variant="body2" noWrap>
          {variantName || '-'}
        </Typography>
        {variantSku && (
          <Typography variant="caption" color="text.disabled" noWrap>
            {variantSku}
          </Typography>
        )}
      </TableCell>

      <TableCell align="center">
        <Typography
          variant="body2"
          sx={{
            fontWeight: 'bold',
            color: quantity > 0 ? 'success.main' : quantity < 0 ? 'error.main' : 'text.primary',
          }}
        >
          {quantityDisplay || (quantity > 0 ? `+${quantity}` : quantity < 0 ? `${quantity}` : quantity)}
        </Typography>
        {notes && (
          <Typography variant="caption" color="text.disabled" sx={{ display: 'block', mt: 0.5 }}>
            {notes.length > 30 ? `${notes.substring(0, 30)}...` : notes}
          </Typography>
        )}
      </TableCell>

      <TableCell align="center">
        <Typography variant="body2">{previousQuantity || 0}</Typography>
      </TableCell>

      <TableCell align="center">
        <Typography variant="body2">{currentQuantity || 0}</Typography>
      </TableCell>
    </TableRow>
  );
}
