'use client';

import PropTypes from 'prop-types';
import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TableContainer from '@mui/material/TableContainer';
import CircularProgress from '@mui/material/CircularProgress';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';
import { fetchData, updateData } from 'src/actions/mooly-chatbot/supabase-utils';
import { updateStockWithStoredProcedure } from 'src/actions/mooly-chatbot/inventory-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { FileThumbnail } from 'src/components/file-thumbnail';

// ----------------------------------------------------------------------

export default function InventoryQuickUpdateDialog({ open, onClose, product, onSuccess }) {
  const [variants, setVariants] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [totalStock, setTotalStock] = useState(0);
  const [simpleStock, setSimpleStock] = useState(0);
  const [isSimpleProduct, setIsSimpleProduct] = useState(false);

  // Tải dữ liệu biến thể từ Supabase
  const loadVariants = useCallback(async () => {
    if (!product?.id) return;

    setIsLoading(true);
    try {
      const { data, error } = await fetchData('product_variants', {
        filters: { product_id: product.id },
        orderBy: 'name',
      });

      if (error) throw error;

      setVariants(data);
    } catch (error) {
      console.error('Error loading variants:', error);
      toast.error('Không thể tải dữ liệu biến thể');
    } finally {
      setIsLoading(false);
    }
  }, [product]);

  // Tải dữ liệu biến thể khi dialog mở và kiểm tra loại sản phẩm
  useEffect(() => {
    if (open && product?.id) {
      // Kiểm tra loại sản phẩm
      const isSimple = product.type === PRODUCT_TYPES.SIMPLE;
      setIsSimpleProduct(isSimple);

      if (isSimple) {
        // Nếu là sản phẩm đơn giản, khởi tạo giá trị tồn kho
        setSimpleStock(product.stockQuantity || 0);
      } else {
        // Nếu là sản phẩm biến thể, tải dữ liệu biến thể
        loadVariants();
      }
    }
  }, [open, product, loadVariants]);

  // Tính tổng tồn kho khi variants thay đổi
  useEffect(() => {
    if (variants.length > 0) {
      const total = variants.reduce((sum, variant) => sum + (variant.stockQuantity || 0), 0);
      setTotalStock(total);
    }
  }, [variants]);

  // Xử lý thay đổi tab
  const handleChangeTab = (event, newValue) => {
    setTabValue(newValue);
  };

  // Xử lý thay đổi giá trị tồn kho của biến thể
  const handleStockChange = useCallback((variantId, value) => {
    setVariants((prev) =>
      prev.map((variant) =>
        variant.id === variantId ? { ...variant, stockQuantity: Number(value) || 0 } : variant
      )
    );
  }, []);

  // Xử lý thay đổi giá trị tồn kho cho sản phẩm đơn giản
  const handleSimpleStockChange = useCallback((value) => {
    setSimpleStock(Number(value) || 0);
  }, []);

  // Xử lý cập nhật tồn kho
  const handleSaveInventory = async () => {
    setIsSaving(true);
    const loadingToast = toast.loading('Đang cập nhật tồn kho...');

    try {
      let results = [];

      if (isSimpleProduct) {
        // Cập nhật tồn kho cho sản phẩm đơn giản sử dụng stored procedure
        const result = await updateStockWithStoredProcedure(
          product.id,
          simpleStock,
          null,
          {
            notes: 'Cập nhật tồn kho sản phẩm đơn giản từ dialog',
            referenceType: 'bulk_update'
          }
        );
        results = [result];
      } else {
        // Cập nhật tồn kho cho từng biến thể sử dụng stored procedure
        const updatePromises = variants.map((variant) =>
          updateStockWithStoredProcedure(
            product.id,
            variant.stockQuantity,
            variant.id,
            {
              notes: `Cập nhật tồn kho biến thể ${variant.name} từ dialog`,
              referenceType: 'bulk_update'
            }
          )
        );

        // Thực hiện tất cả các cập nhật
        results = await Promise.all(updatePromises);
      }

      // Kiểm tra lỗi
      const errors = results.filter((result) => !result.success);
      if (errors.length > 0) {
        throw new Error(`Có ${errors.length} lỗi khi cập nhật tồn kho`);
      }

      toast.dismiss(loadingToast);
      toast.success('Cập nhật tồn kho thành công');

      // Gọi callback thành công
      if (onSuccess) {
        onSuccess();
      }

      onClose();
    } catch (error) {
      console.error('Error updating inventory:', error);
      toast.dismiss(loadingToast);
      toast.error(`Lỗi: ${error.message || 'Không thể cập nhật tồn kho'}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          maxHeight: '80vh',
        },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Cập nhật tồn kho</Typography>
          <IconButton edge="end" color="inherit" onClick={onClose}>
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent dividers>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Stack spacing={3}>
            <Card>
              {isSimpleProduct ? (
                // Giao diện cho sản phẩm đơn giản
                <Box sx={{ p: 3 }}>
                  <Stack spacing={3}>
                    <Typography variant="subtitle1">
                      Cập nhật tồn kho cho sản phẩm: <strong>{product?.name}</strong>
                    </Typography>

                    <Stack direction="row" spacing={2} alignItems="center">
                      <Typography variant="body1" sx={{ minWidth: 150 }}>
                        Tồn kho hiện tại:
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {simpleStock} sản phẩm
                      </Typography>
                    </Stack>

                    <Stack direction="row" spacing={2} alignItems="center">
                      <Typography variant="body1" sx={{ minWidth: 150 }}>
                        Cập nhật tồn kho:
                      </Typography>
                      <TextField
                        type="number"
                        size="small"
                        value={simpleStock}
                        onChange={(e) => handleSimpleStockChange(e.target.value)}
                        InputProps={{
                          inputProps: { min: 0 },
                        }}
                      />
                    </Stack>

                    <Box sx={{ bgcolor: 'background.neutral', p: 2, borderRadius: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Tồn kho được quản lý trực tiếp trên sản phẩm. Khi bạn cập nhật tồn kho, số lượng sẽ được cập nhật ngay lập tức.
                      </Typography>
                    </Box>
                  </Stack>
                </Box>
              ) : (
                // Giao diện cho sản phẩm biến thể
                <>
                  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={tabValue} onChange={handleChangeTab}>
                      <Tab label="Tồn kho theo biến thể" />
                      <Tab label="Tổng quan tồn kho" />
                    </Tabs>
                  </Box>

                  {tabValue === 0 && (
                    <TableContainer sx={{ maxHeight: '50vh' }}>
                      <Table stickyHeader>
                        <TableHead>
                          <TableRow>
                            <TableCell>Ảnh</TableCell>
                            <TableCell>Biến thể</TableCell>
                            <TableCell>Tồn kho hiện tại</TableCell>
                            <TableCell>Cập nhật tồn kho</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {variants.map((variant) => (
                            <TableRow key={variant.id}>
                              <TableCell>
                                <Box sx={{ width: 48, height: 48, borderRadius: 1, overflow: 'hidden' }}>
                                  {variant.avatar ? (
                                    <FileThumbnail
                                      file={variant.avatar}
                                      imageView
                                      sx={{ width: '100%', height: '100%' }}
                                    />
                                  ) : (
                                    <Box
                                      sx={{
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        bgcolor: 'background.neutral',
                                      }}
                                    >
                                      <Iconify icon="eva:image-fill" width={24} />
                                    </Box>
                                  )}
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">{variant.name}</Typography>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">{variant.stockQuantity || 0}</Typography>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  type="number"
                                  size="small"
                                  value={variant.stockQuantity || 0}
                                  onChange={(e) => handleStockChange(variant.id, e.target.value)}
                                  InputProps={{
                                    inputProps: { min: 0 },
                                  }}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}

                  {tabValue === 1 && (
                    <Box sx={{ p: 3 }}>
                      <Stack spacing={3}>
                        <Typography variant="subtitle1">
                          Tổng tồn kho: <strong>{totalStock}</strong> sản phẩm
                        </Typography>

                        <Box sx={{ bgcolor: 'background.neutral', p: 2, borderRadius: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Tổng tồn kho được tính bằng tổng tồn kho của tất cả các biến thể. Khi bạn cập nhật tồn kho cho từng biến thể, tổng tồn kho sẽ được tự động cập nhật.
                          </Typography>
                        </Box>
                      </Stack>
                    </Box>
                  )}
                </>
              )}
            </Card>
          </Stack>
        )}
      </DialogContent>

      <DialogActions>
        <Button color="inherit" variant="outlined" onClick={onClose} disabled={isSaving}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleSaveInventory}
          disabled={isSaving || isLoading}
          startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
        >
          {isSaving ? 'Đang lưu...' : 'Lưu thay đổi'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

InventoryQuickUpdateDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  product: PropTypes.object,
  onSuccess: PropTypes.func,
};
