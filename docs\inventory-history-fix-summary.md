# Fix Inventory History Tracking - Summary

## Vấn đề đã được phát hiện

Hệ thống cập nhật kho hàng không ghi nhận lịch sử một cách đồng bộ vì:

1. **UI cập nhật trực tiếp vào database**: Các component UI đang sử dụng `updateData()` để cập nhật trực tiếp vào bảng `products` và `product_variants` thay vì sử dụng stored procedure.

2. **Lịch sử được tạo riêng biệt**: Việc tạo bản ghi `inventory_transactions` được thực hiện riêng biệt sau khi cập nhật stock, dẫn đến khả năng mất đồng bộ.

3. **Function constraint không phù hợp**: Stored procedure `update_product_inventory` sử dụng type không phù hợp với constraint của bảng `inventory_transactions`.

## Giải pháp đã triển khai

### 1. Sửa Stored Procedure `update_product_inventory`

**Vấn đề**: Function sử dụng type 'increase', 'decrease' không có trong constraint.

**Giải pháp**: 
- Cập nhật function để sử dụng type phù hợp: 'adjustment', 'sale', 'return', 'purchase'
- Thay đổi signature function để đặt `quantity_change` làm tham số thứ 2 (required)
- Logic xác định type dựa trên `reference_type` và `quantity_change`

```sql
-- Signature mới
update_product_inventory(
  product_id UUID,
  quantity_change INTEGER,
  variant_id UUID DEFAULT NULL,
  reference_id UUID DEFAULT NULL,
  reference_type VARCHAR DEFAULT 'adjustment',
  notes TEXT DEFAULT NULL
)
```

### 2. Tạo Function Wrapper Mới

**File**: `src/actions/mooly-chatbot/inventory-service.js`

**Function mới**: `updateStockWithStoredProcedure()`
- Sử dụng stored procedure thay vì cập nhật trực tiếp
- Tự động tính toán `quantity_change` từ số lượng hiện tại và số lượng mới
- Ghi lịch sử tự động thông qua stored procedure

### 3. Cập nhật UI Components

**Files đã cập nhật**:
- `src/sections/mooly-chatbot/inventory/view/product-inventory-view.jsx`
- `src/sections/mooly-chatbot/products/inventory-quick-update-dialog.jsx`

**Thay đổi**:
- Thay thế logic cập nhật trực tiếp bằng `updateStockWithStoredProcedure()`
- Loại bỏ việc tạo `inventory_transactions` riêng biệt
- Giữ nguyên logic cập nhật UI state

### 4. Cập nhật Các Service Functions

**Files đã cập nhật**:
- `src/actions/mooly-chatbot/inventory-service.js`
  - `updateInventoryQuantity()`: Sử dụng stored procedure
  - `adjustInventory()`: Sử dụng stored procedure
- `src/actions/mooly-chatbot/inventory-optimization-service.js`
  - `updateStockInDatabase()`: Sử dụng stored procedure
- `src/actions/mooly-chatbot/order-workflow-service.js`
  - `updateInventoryForOrder()`: Cập nhật signature function call

## Kết quả

### ✅ Đã Fix
1. **Lịch sử được ghi nhận tự động**: Mọi cập nhật kho hàng từ UI đều tự động tạo bản ghi lịch sử
2. **Đồng bộ hóa dữ liệu**: Stored procedure đảm bảo cập nhật stock và lịch sử trong cùng transaction
3. **Tối ưu hóa code**: Loại bỏ logic duplicate, sử dụng function dùng chung
4. **Type constraint phù hợp**: Function sử dụng đúng type được cho phép trong constraint

### 🧪 Test Results
```sql
-- Test function
SELECT update_product_inventory(
    '1ae5ede7-d16e-4a29-8b9d-2e340ba013a3'::uuid,  -- product_id
    5,                                               -- quantity_change (+5)
    NULL::uuid,                                      -- variant_id
    NULL::uuid,                                      -- reference_id
    'adjustment',                                    -- reference_type
    'Test cập nhật tồn kho từ stored procedure'      -- notes
);

-- Result: ✅ Success
{
  "success": true,
  "product_id": "1ae5ede7-d16e-4a29-8b9d-2e340ba013a3",
  "previous_stock": 2,
  "new_stock": 7,
  "quantity_change": 5,
  "transaction_type": "adjustment"
}
```

### 📊 Inventory History
Lịch sử được ghi nhận đầy đủ với các thông tin:
- `type`: adjustment, sale, return, purchase
- `quantity`: Số lượng thay đổi (luôn dương)
- `previous_quantity`: Số lượng trước khi thay đổi
- `current_quantity`: Số lượng sau khi thay đổi
- `reference_type`: Loại tham chiếu (manual_adjustment, order, etc.)
- `notes`: Ghi chú chi tiết

## Lưu ý cho Developer

1. **Sử dụng `updateStockWithStoredProcedure()`** thay vì cập nhật trực tiếp khi cần cập nhật tồn kho từ UI
2. **Function signature mới** yêu cầu `quantity_change` là tham số thứ 2
3. **Tất cả cập nhật kho hàng** giờ đây đều tự động ghi lịch sử
4. **Không cần tạo `inventory_transactions` thủ công** nữa khi sử dụng stored procedure

## Migration Notes

- ✅ Backward compatible: Các function cũ vẫn hoạt động
- ✅ Database schema không thay đổi
- ✅ UI/UX không thay đổi
- ✅ Chỉ thay đổi logic internal
