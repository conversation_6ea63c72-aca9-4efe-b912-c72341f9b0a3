'use client';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import CircularProgress from '@mui/material/CircularProgress';

import { snakeToCamelObject } from 'src/utils/format-data/case-converter';

import { fetchData } from 'src/actions/mooly-chatbot/supabase-utils';

import { Scrollbar } from 'src/components/scrollbar';
import {
  useTable,
  emptyRows,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import InventoryHistoryTableRow from '../inventory-history-table-row';
import InventoryHistoryTableToolbar from '../inventory-history-table-toolbar';
import InventoryHistoryTableFilters from '../inventory-history-table-filters';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'createdAt', label: 'Ngày', width: '15%' },
  { id: 'type', label: 'Loại giao dịch', width: '15%' },
  { id: 'productName', label: 'Sản phẩm', width: '25%' },
  { id: 'variantName', label: 'Biến thể', width: '15%' },
  { id: 'quantity', label: 'Số lượng', align: 'center', width: '10%' },
  { id: 'previousQuantity', label: 'Trước', align: 'center', width: '10%' },
  { id: 'currentQuantity', label: 'Sau', align: 'center', width: '10%' },
];

// Các loại giao dịch kho hàng
const TRANSACTION_TYPES = [
  { value: 'all', label: 'Tất cả' },
  { value: 'adjustment', label: 'Điều chỉnh tồn kho' },
  { value: 'sale', label: 'Bán hàng' },
  { value: 'purchase', label: 'Nhập kho' },
  { value: 'return', label: 'Trả hàng' },
  { value: 'transfer', label: 'Chuyển kho' },
];

const defaultFilters = {
  name: '',
  type: 'all',
  startDate: null,
  endDate: null,
};

// ----------------------------------------------------------------------

export default function InventoryHistoryView() {
  const table = useTable();

  const [tableData, setTableData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const [filters, setFilters] = useState(defaultFilters);
  const [openFilters, setOpenFilters] = useState(false);

  // Tạo danh sách bộ lọc
  const filterOptions = [
    {
      id: 'type',
      label: 'Loại giao dịch',
      options: TRANSACTION_TYPES,
    },
  ];

  // Lấy lịch sử giao dịch kho hàng
  const fetchInventoryTransactions = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Sử dụng fetchData từ supabase-utils để lấy dữ liệu lịch sử giao dịch
      const { success, data: transactions, error: transactionsError } = await fetchData('inventory_transactions', {
        columns: `
          id,
          type,
          quantity,
          previous_quantity,
          current_quantity,
          notes,
          reference_type,
          created_at,
          updated_at,
          product_id,
          variant_id,
          product:products!product_id(
            id,
            name,
            sku,
            avatar,
            images
          ),
          variant:product_variants!variant_id(
            id,
            name,
            sku,
            attributes
          )
        `,
        orderBy: 'created_at',
        ascending: false,
        limit: 1000,
      });

      if (!success || transactionsError) {
        throw new Error(transactionsError || 'Không thể tải dữ liệu lịch sử kho hàng');
      }

      // Xử lý dữ liệu để hiển thị trong bảng
      const formattedData = transactions.map((transaction) => {
        const productName = transaction.product?.name || 'Sản phẩm đã xóa';
        const variantName = transaction.variant?.name ||
          (transaction.variant?.attributes ?
            Object.values(transaction.variant.attributes).join(' - ') :
            (transaction.variantId ? 'Biến thể đã xóa' : ''));

        // Xác định loại giao dịch hiển thị
        let displayType = transaction.type;
        switch (transaction.type) {
          case 'adjustment':
            displayType = 'Điều chỉnh';
            break;
          case 'sale':
            displayType = 'Bán hàng';
            break;
          case 'return':
            displayType = 'Trả hàng';
            break;
          case 'purchase':
            displayType = 'Nhập kho';
            break;
          default:
            displayType = transaction.type;
        }

        return {
          ...transaction,
          productName,
          variantName,
          displayType,
          productSku: transaction.product?.sku || '',
          variantSku: transaction.variant?.sku || '',
          productAvatar: transaction.product?.avatar ||
            (transaction.product?.images && transaction.product.images.length > 0 ?
              transaction.product.images[0] : null),
          // Định dạng số lượng với dấu +/- để dễ nhìn
          quantityDisplay: transaction.quantity > 0 ? `+${transaction.quantity}` : transaction.quantity.toString(),
        };
      });

      setTableData(snakeToCamelObject(formattedData));
    } catch (err) {
      console.error('Error fetching inventory transactions:', err);
      setError(err.message || 'Không thể tải lịch sử kho hàng. Vui lòng thử lại sau.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Tải dữ liệu khi component được mount
  useEffect(() => {
    fetchInventoryTransactions();
     
  }, []);

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const denseHeight = table.dense ? 60 : 80;

  const canReset =
    !!filters.name ||
    filters.type !== 'all' ||
    (!!filters.startDate && !!filters.endDate);

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleFilters = useCallback(
    (name, value) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  const handleOpenFilters = useCallback(() => {
    setOpenFilters(true);
  }, []);

  const handleCloseFilters = useCallback(() => {
    setOpenFilters(false);
  }, []);

  const handleViewDetails = useCallback((id) => {
    // Hiển thị thông báo chi tiết giao dịch
    console.log('View details', id);
    // Trong tương lai có thể mở dialog hiển thị chi tiết giao dịch
  }, []);

  return (
    <Card>
        <InventoryHistoryTableToolbar
          filters={filters}
          onFilters={handleFilters}
          //
          canReset={canReset}
          onResetFilters={handleResetFilters}
          //
          onOpenFilters={handleOpenFilters}
        />

        <InventoryHistoryTableFilters
          open={openFilters}
          onClose={handleCloseFilters}
          //
          filters={filters}
          onFilters={handleFilters}
          //
          filterOptions={filterOptions}
          //
          onResetFilters={handleResetFilters}
        />

        {error && (
          <Stack sx={{ p: 3 }}>
            <Alert severity="error">{error}</Alert>
          </Stack>
        )}

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
              <Scrollbar>
                <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                  <TableHeadCustom
                    order={table.order}
                    orderBy={table.orderBy}
                    headCells={TABLE_HEAD}
                    onSort={table.onSort}
                  />

                  <TableBody>
                    {dataFiltered
                      .slice(
                        table.page * table.rowsPerPage,
                        table.page * table.rowsPerPage + table.rowsPerPage
                      )
                      .map((row) => (
                        <InventoryHistoryTableRow
                          key={row.id}
                          row={row}
                          onViewDetails={() => handleViewDetails(row.id)}
                        />
                      ))}

                    <TableEmptyRows
                      height={denseHeight}
                      emptyRows={emptyRows(table.page, table.rowsPerPage, tableData.length)}
                    />

                    <TableNoData notFound={notFound} />
                  </TableBody>
                </Table>
              </Scrollbar>
            </TableContainer>

            <TablePaginationCustom
              count={dataFiltered.length}
              page={table.page}
              rowsPerPage={table.rowsPerPage}
              onPageChange={table.onChangePage}
              onRowsPerPageChange={table.onChangeRowsPerPage}
              //
              dense={table.dense}
              onChangeDense={table.onChangeDense}
            />
          </>
        )}
      </Card>
  );
}

// ----------------------------------------------------------------------

function applyFilter({ inputData, comparator, filters }) {
  const { name, type, startDate, endDate } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index]);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (item) => item.productName.toLowerCase().indexOf(name.toLowerCase()) !== -1
    );
  }

  if (type !== 'all') {
    inputData = inputData.filter((item) => item.type === type);
  }

  if (startDate && endDate) {
    inputData = inputData.filter(
      (item) => new Date(item.createdAt) >= startDate && new Date(item.createdAt) <= endDate
    );
  }

  return inputData;
}
